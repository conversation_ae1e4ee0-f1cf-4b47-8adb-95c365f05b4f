import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { prisma } from '@/lib/prisma';

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ childId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const child = await prisma.child.findUnique({
      where: { id: resolvedParams.childId },
      include: {
        sessionNotes: {
          orderBy: { date: 'desc' }
        },
        caseStudy: true,
        _count: {
          select: {
            assessments: true,
            learningPlans: true,
            sessionNotes: true,
          },
        },
      },
    });

    if (!child) {
      return NextResponse.json({ error: 'Child not found' }, { status: 404 });
    }

    return NextResponse.json(child);
  } catch (error) {
    console.error('Error fetching child:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ childId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const data = await request.json();

    // Extract session notes and case study data from the request
    const { sessionNotes, caseStudy, ...childData } = data;

    // Update the child record
    await prisma.child.update({
      where: { id: resolvedParams.childId },
      data: childData,
    });

    // Handle session notes if provided
    if (sessionNotes && Array.isArray(sessionNotes)) {
      // Get existing session notes
      const existingNotes = await prisma.sessionNote.findMany({
        where: { childId: resolvedParams.childId }
      });

      // Find new notes (those not in the database)
      const newNotes = sessionNotes.filter(note =>
        !existingNotes.some(existing => existing.id === note.id)
      );

      // Create new session notes
      if (newNotes.length > 0) {
        await prisma.sessionNote.createMany({
          data: newNotes.map(note => ({
            id: note.id,
            childId: resolvedParams.childId,
            date: new Date(note.date),
            goalDiscussed: note.goalDiscussed,
            attendees: note.attendees,
            notes: note.notes,
            nextSteps: note.nextSteps || null,
          }))
        });
      }
    }

    // Handle case study data if provided
    if (caseStudy) {
      await prisma.caseStudyData.upsert({
        where: { childId: resolvedParams.childId },
        update: {
          basicInfo: caseStudy.basicInfo,
          pregnancyAndBirthInfo: caseStudy.pregnancyAndBirthInfo,
          reinforcerResponseInfo: caseStudy.reinforcerResponseInfo,
        },
        create: {
          childId: resolvedParams.childId,
          basicInfo: caseStudy.basicInfo,
          pregnancyAndBirthInfo: caseStudy.pregnancyAndBirthInfo,
          reinforcerResponseInfo: caseStudy.reinforcerResponseInfo,
        },
      });
    }

    // Fetch the updated child with all related data
    const finalChild = await prisma.child.findUnique({
      where: { id: resolvedParams.childId },
      include: {
        sessionNotes: {
          orderBy: { date: 'desc' }
        },
        caseStudy: true,
        _count: {
          select: {
            assessments: true,
            learningPlans: true,
            sessionNotes: true,
          },
        },
      },
    });

    return NextResponse.json(finalChild);
  } catch (error) {
    console.error('Error updating child:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ childId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    await prisma.child.delete({
      where: { id: resolvedParams.childId },
    });
    return new Response(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting child:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
