import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import type { Child, Assessment, User, LearningPlan, ComprehensiveReport, PlanNote, CaseStudyData } from '@/lib/types';

// API client with authentication
async function apiClient<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return response.json();
}

// Query keys for consistent caching
export const queryKeys = {
  children: ['children'] as const,
  child: (id: string) => ['children', id] as const,
  assessments: ['assessments'] as const,
  assessment: (id: string) => ['assessments', id] as const,
  childAssessments: (childId: string) => ['assessments', 'child', childId] as const,
  users: ['users'] as const,
  user: (id: string) => ['users', id] as const,
  learningPlans: ['learningPlans'] as const,
  childLearningPlans: (childId: string) => ['learningPlans', 'child', childId] as const,
  comprehensiveReports: ['comprehensiveReports'] as const,
  childReports: (childId: string) => ['comprehensiveReports', 'child', childId] as const,
  planNotes: ['planNotes'] as const,
  childPlanNotes: (childId: string) => ['planNotes', 'child', childId] as const,
  caseStudy: (childId: string) => ['caseStudy', childId] as const,
};

// Children hooks
export function useChildren() {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: queryKeys.children,
    queryFn: () => apiClient<Child[]>('/api/children'),
    enabled: !!session,
  });
}

export function useChild(id: string) {
  const { data: session } = useSession();

  return useQuery({
    queryKey: queryKeys.child(id),
    queryFn: () => apiClient<Child>(`/api/children/${id}`),
    enabled: !!session && !!id && id !== 'undefined',
  });
}

export function useCreateChild() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<Child>) => apiClient<Child>('/api/children', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.children });
    },
  });
}

export function useUpdateChild() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Child> }) =>
      apiClient<Child>(`/api/children/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.children });
      queryClient.invalidateQueries({ queryKey: queryKeys.child(id) });
    },
  });
}

export function useDeleteChild() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => apiClient(`/api/children/${id}`, {
      method: 'DELETE',
    }),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.children });
      queryClient.removeQueries({ queryKey: queryKeys.child(id) });
    },
  });
}

// Assessment hooks
export function useAssessments(childId?: string) {
  const { data: session } = useSession();

  return useQuery({
    queryKey: childId ? queryKeys.childAssessments(childId) : queryKeys.assessments,
    queryFn: () => {
      const url = childId && childId !== 'undefined' ? `/api/assessments?childId=${childId}` : '/api/assessments';
      return apiClient<Assessment[]>(url);
    },
    enabled: !!session,
  });
}

export function useAssessment(id: string) {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: queryKeys.assessment(id),
    queryFn: () => apiClient<Assessment>(`/api/assessments/${id}`),
    enabled: !!session && !!id,
  });
}

export function useCreateAssessment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<Assessment>) => apiClient<Assessment>('/api/assessments', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    onSuccess: (newAssessment) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.assessments });
      if (newAssessment.childId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.childAssessments(newAssessment.childId) });
      }
    },
  });
}

// User hooks
export function useUsers() {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: queryKeys.users,
    queryFn: () => apiClient<User[]>('/api/users'),
    enabled: !!session && ['super_admin', 'eiu_manager'].includes(session.user.role),
  });
}

// Learning Plan hooks
export function useLearningPlans(childId?: string) {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: childId ? queryKeys.childLearningPlans(childId) : queryKeys.learningPlans,
    queryFn: () => {
      const url = childId ? `/api/learning-plans?childId=${childId}` : '/api/learning-plans';
      return apiClient<LearningPlan[]>(url);
    },
    enabled: !!session,
  });
}

export function useCreateLearningPlan() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<LearningPlan>) => apiClient<LearningPlan>('/api/learning-plans', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    onSuccess: (newPlan) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.learningPlans });
      if (newPlan.childId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.childLearningPlans(newPlan.childId) });
      }
    },
  });
}

// Comprehensive Report hooks
export function useComprehensiveReports(childId?: string) {
  const { data: session } = useSession();

  return useQuery({
    queryKey: childId ? queryKeys.childReports(childId) : queryKeys.comprehensiveReports,
    queryFn: () => {
      const url = childId ? `/api/comprehensive-reports?childId=${childId}` : '/api/comprehensive-reports';
      return apiClient<ComprehensiveReport[]>(url);
    },
    enabled: !!session,
  });
}

export function useCreateComprehensiveReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<ComprehensiveReport, 'id' | 'generatedDate'>) =>
      apiClient<ComprehensiveReport>('/api/comprehensive-reports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.comprehensiveReports });
    },
  });
}

// Plan Notes hooks
export function usePlanNotes(childId?: string) {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: childId ? queryKeys.childPlanNotes(childId) : queryKeys.planNotes,
    queryFn: () => {
      const url = childId ? `/api/plan-notes?childId=${childId}` : '/api/plan-notes';
      return apiClient<PlanNote[]>(url);
    },
    enabled: !!session,
  });
}

export function useCreatePlanNote() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<PlanNote>) => apiClient<PlanNote>('/api/plan-notes', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    onSuccess: (newNote) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.planNotes });
      if (newNote.childId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.childPlanNotes(newNote.childId) });
      }
    },
  });
}

// Case Study hooks
export function useCaseStudy(childId: string) {
  const { data: session } = useSession();
  
  return useQuery({
    queryKey: queryKeys.caseStudy(childId),
    queryFn: () => apiClient<CaseStudyData>(`/api/case-study/${childId}`),
    enabled: !!session && !!childId,
  });
}

export function useUpdateCaseStudy() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ childId, data }: { childId: string; data: Partial<CaseStudyData> }) =>
      apiClient<CaseStudyData>(`/api/case-study/${childId}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    onSuccess: (_, { childId }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.caseStudy(childId) });
    },
  });
}
